"""
社区模块API路由
"""
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
# 导入数据库模型
from app.models.community import (
    LearningResource, StudyRoom, Topic, TopicReply,
    Tribe, TribeMember, MentorProfile, MentorshipRelation, Experience,
    Activity, Wish, WishComment, GameActivity, GameParticipant
)
# 导入响应模型
from app.schemas.community import (
    LearningResourceResponse, StudyRoomResponse, TopicResponse,
    TribeResponse, MentorProfileResponse, MentorshipRelationResponse,
    ExperienceResponse, ActivityResponse, WishResponse, GameActivityResponse,
    CommunityStatistics, PagedResponse
)
from app.core.security import get_current_user
from app.models import AdminUser as User

router = APIRouter()


# ==================== 资源库管理 ====================

@router.get("/resources", response_model=PagedResponse[LearningResourceResponse])
async def get_resources(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    keyword: Optional[str] = None,
    type: Optional[str] = None,
    status: Optional[str] = None,
    subject: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取学习资源列表"""
    query = db.query(LearningResource)
    
    # 应用筛选条件
    if keyword:
        query = query.filter(
            LearningResourceResponse.title.contains(keyword) |
            LearningResourceResponse.description.contains(keyword)
        )
    if type:
        query = query.filter(LearningResourceResponse.type == type)
    if status:
        query = query.filter(LearningResourceResponse.status == status)
    if subject:
        query = query.filter(LearningResourceResponse.subject == subject)
    
    # 分页
    total = query.count()
    resources = query.offset((page - 1) * size).limit(size).all()
    
    return PagedResponse(
        items=resources,
        total=total,
        page=page,
        size=size,
        total_pages=(total + size - 1) // size,
        has_next=page * size < total,
        has_prev=page > 1
    )


@router.get("/resources/{resource_id}", response_model=LearningResourceResponse)
async def get_resource(
    resource_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取资源详情"""
    resource = db.query(LearningResourceResponse).filter(LearningResourceResponse.id == resource_id).first()
    if not resource:
        raise HTTPException(status_code=404, detail="资源不存在")
    return resource


@router.put("/resources/{resource_id}/approve")
async def approve_resource(
    resource_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """审核通过资源"""
    resource = db.query(LearningResourceResponse).filter(LearningResourceResponse.id == resource_id).first()
    if not resource:
        raise HTTPException(status_code=404, detail="资源不存在")
    
    resource.status = "approved"
    resource.is_approved = True
    resource.approved_by = current_user.id
    resource.approved_at = datetime.utcnow()
    
    db.commit()
    return {"message": "资源审核通过"}


@router.put("/resources/{resource_id}/reject")
async def reject_resource(
    resource_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """拒绝资源"""
    resource = db.query(LearningResourceResponse).filter(LearningResourceResponse.id == resource_id).first()
    if not resource:
        raise HTTPException(status_code=404, detail="资源不存在")
    
    resource.status = "rejected"
    resource.approved_by = current_user.id
    resource.approved_at = datetime.utcnow()
    
    db.commit()
    return {"message": "资源已拒绝"}


@router.delete("/resources/{resource_id}")
async def delete_resource(
    resource_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除资源"""
    resource = db.query(LearningResourceResponse).filter(LearningResourceResponse.id == resource_id).first()
    if not resource:
        raise HTTPException(status_code=404, detail="资源不存在")
    
    db.delete(resource)
    db.commit()
    return {"message": "资源已删除"}


# ==================== 自习室管理 ====================

@router.get("/study-rooms", response_model=PagedResponse[StudyRoomResponse])
async def get_study_rooms(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    keyword: Optional[str] = None,
    status: Optional[str] = None,
    is_public: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取自习室列表"""
    query = db.query(StudyRoomResponse)
    
    # 应用筛选条件
    if keyword:
        query = query.filter(
            StudyRoomResponse.name.contains(keyword) |
            StudyRoomResponse.description.contains(keyword)
        )
    if status:
        query = query.filter(StudyRoomResponse.status == status)
    if is_public is not None:
        query = query.filter(StudyRoomResponse.is_public == is_public)
    
    # 分页
    total = query.count()
    rooms = query.offset((page - 1) * size).limit(size).all()
    
    return PagedResponse(
        items=rooms,
        total=total,
        page=page,
        size=size,
        total_pages=(total + size - 1) // size,
        has_next=page * size < total,
        has_prev=page > 1
    )


@router.get("/study-rooms/{room_id}/members")
async def get_study_room_members(
    room_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取自习室成员列表"""
    members = db.query(StudyRoomResponse).filter(StudyRoomResponse.room_id == room_id).all()
    return members


@router.delete("/study-rooms/{room_id}")
async def delete_study_room(
    room_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除自习室"""
    room = db.query(StudyRoomResponse).filter(StudyRoomResponse.id == room_id).first()
    if not room:
        raise HTTPException(status_code=404, detail="自习室不存在")
    
    # 删除相关成员记录
    db.query(StudyRoomResponse).filter(StudyRoomResponse.room_id == room_id).delete()
    db.delete(room)
    db.commit()
    return {"message": "自习室已删除"}


# ==================== 话题管理 ====================

@router.get("/topics", response_model=PagedResponse[TopicResponse])
async def get_topics(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    keyword: Optional[str] = None,
    status: Optional[str] = None,
    category: Optional[str] = None,
    is_hot: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取话题列表"""
    query = db.query(TopicReply)
    
    # 应用筛选条件
    if keyword:
        query = query.filter(
            TopicReply.title.contains(keyword) |
            TopicReply.content.contains(keyword)
        )
    if status:
        query = query.filter(TopicReply.status == status)
    if category:
        query = query.filter(TopicReply.category_name == category)
    if is_hot is not None:
        query = query.filter(TopicReply.is_hot == is_hot)
    
    # 分页
    total = query.count()
    topics = query.offset((page - 1) * size).limit(size).all()
    
    return PagedResponse(
        items=topics,
        total=total,
        page=page,
        size=size,
        total_pages=(total + size - 1) // size,
        has_next=page * size < total,
        has_prev=page > 1
    )


@router.put("/topics/{topic_id}/pin")
async def pin_topic(
    topic_id: str,
    pin: bool,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """置顶/取消置顶话题"""
    topic = db.query(TopicReply).filter(TopicReply.id == topic_id).first()
    if not topic:
        raise HTTPException(status_code=404, detail="话题不存在")
    
    topic.is_pinned = pin
    db.commit()
    return {"message": f"话题已{'置顶' if pin else '取消置顶'}"}


@router.put("/topics/{topic_id}/lock")
async def lock_topic(
    topic_id: str,
    lock: bool,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """锁定/解锁话题"""
    topic = db.query(TopicReply).filter(TopicReply.id == topic_id).first()
    if not topic:
        raise HTTPException(status_code=404, detail="话题不存在")
    
    topic.status = "locked" if lock else "active"
    db.commit()
    return {"message": f"话题已{'锁定' if lock else '解锁'}"}


@router.get("/topics/{topic_id}/replies")
async def get_topic_replies(
    topic_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取话题回复列表"""
    replies = db.query(TopicReply).filter(TopicReply.topic_id == topic_id).all()
    return replies


@router.delete("/topics/{topic_id}")
async def delete_topic(
    topic_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除话题"""
    topic = db.query(TopicReply).filter(TopicReply.id == topic_id).first()
    if not topic:
        raise HTTPException(status_code=404, detail="话题不存在")
    
    # 删除相关回复
    db.query(TopicReply).filter(TopicReply.topic_id == topic_id).delete()
    db.delete(topic)
    db.commit()
    return {"message": "话题已删除"}


# ==================== 部落管理 ====================

@router.get("/tribes", response_model=PagedResponse[TribeMember])
async def get_tribes(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    keyword: Optional[str] = None,
    category: Optional[str] = None,
    level: Optional[int] = None,
    is_public: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取部落列表"""
    query = db.query(TribeMember)
    
    # 应用筛选条件
    if keyword:
        query = query.filter(
            TribeMember.name.contains(keyword) |
            TribeMember.description.contains(keyword)
        )
    if category:
        query = query.filter(TribeMember.category == category)
    if level:
        query = query.filter(TribeMember.level == level)
    if is_public is not None:
        query = query.filter(TribeMember.is_public == is_public)
    
    # 分页
    total = query.count()
    tribes = query.offset((page - 1) * size).limit(size).all()
    
    return PagedResponse(
        items=tribes,
        total=total,
        page=page,
        size=size,
        total_pages=(total + size - 1) // size,
        has_next=page * size < total,
        has_prev=page > 1
    )


@router.get("/tribes/{tribe_id}/members")
async def get_tribe_members(
    tribe_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取部落成员列表"""
    members = db.query(TribeMember).filter(TribeMember.tribe_id == tribe_id).all()
    return members


@router.delete("/tribes/{tribe_id}")
async def delete_tribe(
    tribe_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除部落"""
    tribe = db.query(TribeMember).filter(TribeMember.id == tribe_id).first()
    if not tribe:
        raise HTTPException(status_code=404, detail="部落不存在")
    
    # 删除相关成员记录
    db.query(TribeMember).filter(TribeMember.tribe_id == tribe_id).delete()
    db.delete(tribe)
    db.commit()
    return {"message": "部落已删除"}


# ==================== 师徒结对管理 ====================

@router.get("/mentorships", response_model=PagedResponse[MentorshipRelation])
async def get_mentorships(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    keyword: Optional[str] = None,
    status: Optional[str] = None,
    subject: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取师徒关系列表"""
    query = db.query(MentorshipRelation)

    if keyword:
        query = query.filter(
            MentorshipRelation.mentor_name.contains(keyword) |
            MentorshipRelation.student_name.contains(keyword)
        )
    if status:
        query = query.filter(MentorshipRelation.status == status)
    if subject:
        query = query.filter(MentorshipRelation.subject == subject)

    total = query.count()
    relations = query.offset((page - 1) * size).limit(size).all()

    return PagedResponse(
        items=relations,
        total=total,
        page=page,
        size=size,
        total_pages=(total + size - 1) // size,
        has_next=page * size < total,
        has_prev=page > 1
    )


@router.get("/mentors", response_model=PagedResponse[MentorProfile])
async def get_mentors(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    keyword: Optional[str] = None,
    subject: Optional[str] = None,
    available: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取导师列表"""
    query = db.query(MentorProfile)

    if keyword:
        query = query.filter(MentorProfile.user_name.contains(keyword))
    if subject:
        query = query.filter(MentorProfile.subjects.contains([subject]))
    if available is not None:
        query = query.filter(MentorProfile.is_available == available)

    total = query.count()
    mentors = query.offset((page - 1) * size).limit(size).all()

    return PagedResponse(
        items=mentors,
        total=total,
        page=page,
        size=size,
        total_pages=(total + size - 1) // size,
        has_next=page * size < total,
        has_prev=page > 1
    )


@router.delete("/mentorships/{relation_id}")
async def delete_mentorship(
    relation_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除师徒关系"""
    relation = db.query(MentorshipRelation).filter(MentorshipRelation.id == relation_id).first()
    if not relation:
        raise HTTPException(status_code=404, detail="师徒关系不存在")

    db.delete(relation)
    db.commit()
    return {"message": "师徒关系已删除"}


# ==================== 学霸经验管理 ====================

@router.get("/experiences", response_model=PagedResponse[Experience])
async def get_experiences(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    keyword: Optional[str] = None,
    subject: Optional[str] = None,
    is_recommended: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取学霸经验列表"""
    query = db.query(Experience)

    if keyword:
        query = query.filter(
            Experience.title.contains(keyword) |
            Experience.content.contains(keyword)
        )
    if subject:
        query = query.filter(Experience.subject == subject)
    if is_recommended is not None:
        query = query.filter(Experience.is_recommended == is_recommended)

    total = query.count()
    experiences = query.offset((page - 1) * size).limit(size).all()

    return PagedResponse(
        items=experiences,
        total=total,
        page=page,
        size=size,
        total_pages=(total + size - 1) // size,
        has_next=page * size < total,
        has_prev=page > 1
    )


@router.put("/experiences/{experience_id}/recommend")
async def recommend_experience(
    experience_id: str,
    recommend: bool,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """推荐/取消推荐经验"""
    experience = db.query(Experience).filter(Experience.id == experience_id).first()
    if not experience:
        raise HTTPException(status_code=404, detail="经验不存在")

    experience.is_recommended = recommend
    db.commit()
    return {"message": f"经验已{'推荐' if recommend else '取消推荐'}"}


@router.delete("/experiences/{experience_id}")
async def delete_experience(
    experience_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除经验"""
    experience = db.query(Experience).filter(Experience.id == experience_id).first()
    if not experience:
        raise HTTPException(status_code=404, detail="经验不存在")

    db.delete(experience)
    db.commit()
    return {"message": "经验已删除"}


# ==================== 活动竞赛管理 ====================

@router.get("/activities", response_model=PagedResponse[Activity])
async def get_activities(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    keyword: Optional[str] = None,
    type: Optional[str] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取活动列表"""
    query = db.query(Activity)

    if keyword:
        query = query.filter(
            Activity.title.contains(keyword) |
            Activity.description.contains(keyword)
        )
    if type:
        query = query.filter(Activity.type == type)
    if status:
        query = query.filter(Activity.status == status)

    total = query.count()
    activities = query.offset((page - 1) * size).limit(size).all()

    return PagedResponse(
        items=activities,
        total=total,
        page=page,
        size=size,
        total_pages=(total + size - 1) // size,
        has_next=page * size < total,
        has_prev=page > 1
    )


@router.get("/activities/{activity_id}/participants")
async def get_activity_participants(
    activity_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取活动参与者列表"""
    participants = db.query(Activity).filter(Activity.activity_id == activity_id).all()
    return participants


@router.delete("/activities/{activity_id}")
async def delete_activity(
    activity_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除活动"""
    activity = db.query(Activity).filter(Activity.id == activity_id).first()
    if not activity:
        raise HTTPException(status_code=404, detail="活动不存在")

    # 删除相关参与者记录
    db.query(Activity).filter(Activity.activity_id == activity_id).delete()
    db.delete(activity)
    db.commit()
    return {"message": "活动已删除"}


# ==================== 心愿管理 ====================

@router.get("/wishes", response_model=PagedResponse[WishResponse])
async def get_wishes(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    keyword: Optional[str] = None,
    status: Optional[str] = None,
    is_achieved: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取心愿列表"""
    query = db.query(WishResponse)

    if keyword:
        query = query.filter(WishResponse.content.contains(keyword))
    if status:
        query = query.filter(WishResponse.status == status)
    if is_achieved is not None:
        query = query.filter(WishResponse.is_achieved == is_achieved)

    total = query.count()
    wishes = query.offset((page - 1) * size).limit(size).all()

    return PagedResponse(
        items=wishes,
        total=total,
        page=page,
        size=size,
        total_pages=(total + size - 1) // size,
        has_next=page * size < total,
        has_prev=page > 1
    )


@router.put("/wishes/{wish_id}/achieve")
async def achieve_wish(
    wish_id: str,
    achieve: bool,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """标记/取消心愿实现"""
    wish = db.query(WishResponse).filter(WishResponse.id == wish_id).first()
    if not wish:
        raise HTTPException(status_code=404, detail="心愿不存在")

    wish.is_achieved = achieve
    if achieve:
        wish.achieved_at = datetime.utcnow()
    else:
        wish.achieved_at = None

    db.commit()
    return {"message": f"心愿已{'标记为实现' if achieve else '取消实现'}"}


@router.get("/wishes/{wish_id}/comments")
async def get_wish_comments(
    wish_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取心愿评论列表"""
    comments = db.query(WishResponse).filter(WishResponse.wish_id == wish_id).all()
    return comments


@router.delete("/wishes/{wish_id}")
async def delete_wish(
    wish_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除心愿"""
    wish = db.query(WishResponse).filter(WishResponse.id == wish_id).first()
    if not wish:
        raise HTTPException(status_code=404, detail="心愿不存在")

    # 删除相关评论
    db.query(WishResponse).filter(WishResponse.wish_id == wish_id).delete()
    db.delete(wish)
    db.commit()
    return {"message": "心愿已删除"}


# ==================== 游戏管理 ====================

@router.get("/games", response_model=PagedResponse[GameActivity])
async def get_games(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    keyword: Optional[str] = None,
    type: Optional[str] = None,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取游戏列表"""
    query = db.query(GameActivity)

    if keyword:
        query = query.filter(
            GameActivity.name.contains(keyword) |
            GameActivity.description.contains(keyword)
        )
    if type:
        query = query.filter(GameActivity.type == type)
    if is_active is not None:
        query = query.filter(GameActivity.is_active == is_active)

    total = query.count()
    games = query.offset((page - 1) * size).limit(size).all()

    return PagedResponse(
        items=games,
        total=total,
        page=page,
        size=size,
        total_pages=(total + size - 1) // size,
        has_next=page * size < total,
        has_prev=page > 1
    )


@router.get("/games/{game_id}/participants")
async def get_game_participants(
    game_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取游戏参与者列表"""
    participants = db.query(GameParticipant).filter(GameParticipant.game_id == game_id).all()
    return participants


@router.put("/games/{game_id}/toggle")
async def toggle_game(
    game_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """启用/停用游戏"""
    game = db.query(GameActivity).filter(GameActivity.id == game_id).first()
    if not game:
        raise HTTPException(status_code=404, detail="游戏不存在")

    game.is_active = not game.is_active
    db.commit()
    return {"message": f"游戏已{'启用' if game.is_active else '停用'}"}


@router.delete("/games/{game_id}")
async def delete_game(
    game_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除游戏"""
    game = db.query(GameActivity).filter(GameActivity.id == game_id).first()
    if not game:
        raise HTTPException(status_code=404, detail="游戏不存在")

    # 删除相关参与者记录
    db.query(GameParticipant).filter(GameParticipant.game_id == game_id).delete()
    db.delete(game)
    db.commit()
    return {"message": "游戏已删除"}


# ==================== 统计数据 ====================

@router.get("/statistics", response_model=CommunityStatistics)
async def get_community_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取社区统计数据"""
    stats = CommunityStatistics(
        total_resources=db.query(LearningResourceResponse).count(),
        total_study_rooms=db.query(StudyRoomResponse).count(),
        total_topics=db.query(TopicReply).count(),
        total_tribes=db.query(TribeMember).count(),
        total_mentorships=db.query(MentorshipRelation).count(),
        total_experiences=db.query(Experience).count(),
        total_activities=db.query(Activity).count(),
        total_wishes=db.query(WishResponse).count(),
        total_games=db.query(GameActivity).count(),
        active_users=0,  # TODO: 实现活跃用户统计
        daily_active_users=0,
        weekly_active_users=0,
        monthly_active_users=0
    )
    return stats
